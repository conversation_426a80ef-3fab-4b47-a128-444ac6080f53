import { z } from 'zod';
import type { AgentContext } from '../types';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { createLogger } from '../../log';
import { DataExtractionService, type DataExtractionRequest } from '../../services/dataExtractionService';
import { DataExportService, type ExportOptions } from '../../services/dataExportService';

const logger = createLogger('DataExtractionAction');

// Schema for data extraction action parameters
export const DataExtractionActionSchema = z.object({
  query: z.string().describe('Natural language description of what data to extract'),
  target_fields: z.array(z.string()).optional().describe('Specific fields to extract (optional)'),
  max_items: z.number().default(50).describe('Maximum number of items to extract'),
  format_preference: z.enum(['table', 'list', 'structured']).default('table').describe('Preferred data format'),
  export_format: z.enum(['csv', 'json', 'excel']).optional().describe('Export format if requested'),
  export_filename: z.string().optional().describe('Custom filename for export'),
});

export type DataExtractionActionParams = z.infer<typeof DataExtractionActionSchema>;

export class DataExtractionAction {
  private context: AgentContext;
  private llm: BaseChatModel;
  private extractionService: DataExtractionService;
  private exportService: DataExportService;

  constructor(context: AgentContext, llm: BaseChatModel) {
    this.context = context;
    this.llm = llm;
    this.extractionService = new DataExtractionService(llm, context.browserContext);
    this.exportService = new DataExportService();
  }

  /**
   * Execute data extraction action
   */
  async execute(params: DataExtractionActionParams): Promise<{
    success: boolean;
    data?: any[];
    metadata?: any;
    export_result?: any;
    message: string;
    includeInMemory: boolean;
  }> {
    try {
      logger.info('Executing data extraction action', { query: params.query });

      // Prepare extraction request
      const extractionRequest: DataExtractionRequest = {
        query: params.query,
        target_fields: params.target_fields,
        max_items: params.max_items,
        format_preference: params.format_preference,
        include_metadata: true,
      };

      // Execute data extraction
      const extractionResult = await this.extractionService.extractData(extractionRequest);

      if (!extractionResult.success) {
        return {
          success: false,
          message: `Data extraction failed: ${extractionResult.error || 'Unknown error'}`,
          includeInMemory: true,
        };
      }

      let exportResult;
      let exportMessage = '';

      // Handle export if requested
      if (params.export_format) {
        const exportOptions: ExportOptions = {
          format: params.export_format,
          filename: params.export_filename,
          includeMetadata: true,
        };

        exportResult = await this.exportService.exportData(extractionResult, exportOptions);
        
        if (exportResult.success) {
          exportMessage = ` Data exported to ${exportResult.filename} (${this.formatFileSize(exportResult.size)}).`;
        } else {
          exportMessage = ` Export failed: ${exportResult.error}`;
        }
      }

      // Create success message
      const itemCount = extractionResult.data.length;
      const confidenceScore = Math.round(extractionResult.metadata.confidence_score * 100);
      
      let message = `Successfully extracted ${itemCount} item${itemCount !== 1 ? 's' : ''} from ${new URL(extractionResult.metadata.source_url).hostname}. `;
      message += `Confidence: ${confidenceScore}%. `;
      message += `Method: ${extractionResult.metadata.extraction_method.replace('_', ' ')}.`;
      message += exportMessage;

      // Store extraction result in context for potential follow-up actions
      this.context.lastExtractionResult = extractionResult;

      return {
        success: true,
        data: extractionResult.data,
        metadata: extractionResult.metadata,
        export_result: exportResult,
        message,
        includeInMemory: true,
      };

    } catch (error) {
      logger.error('Data extraction action failed', error);
      return {
        success: false,
        message: `Data extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        includeInMemory: true,
      };
    }
  }

  /**
   * Format file size for display
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Schema for smart data extraction action (with AI planning)
export const SmartDataExtractionActionSchema = z.object({
  task_description: z.string().describe('High-level description of the data extraction task'),
  navigation_required: z.boolean().default(false).describe('Whether navigation to a different page is required'),
  target_url: z.string().optional().describe('URL to navigate to if navigation is required'),
  search_query: z.string().optional().describe('Search query to perform if needed'),
  extraction_steps: z.array(z.object({
    step_description: z.string(),
    extraction_query: z.string(),
    expected_fields: z.array(z.string()).optional(),
  })).describe('Planned extraction steps'),
  final_export_format: z.enum(['csv', 'json', 'excel']).optional().describe('Final export format'),
});

export type SmartDataExtractionActionParams = z.infer<typeof SmartDataExtractionActionSchema>;

export class SmartDataExtractionAction {
  private context: AgentContext;
  private llm: BaseChatModel;
  private extractionAction: DataExtractionAction;

  constructor(context: AgentContext, llm: BaseChatModel) {
    this.context = context;
    this.llm = llm;
    this.extractionAction = new DataExtractionAction(context, llm);
  }

  /**
   * Execute smart data extraction with multi-step planning
   */
  async execute(params: SmartDataExtractionActionParams): Promise<{
    success: boolean;
    results?: any[];
    final_export?: any;
    message: string;
    includeInMemory: boolean;
  }> {
    try {
      logger.info('Executing smart data extraction', { task: params.task_description });

      const allResults: any[] = [];
      let stepMessages: string[] = [];

      // Navigate to target URL if required
      if (params.navigation_required && params.target_url) {
        try {
          await this.context.browserContext.navigateTo(params.target_url);
          stepMessages.push(`Navigated to ${params.target_url}`);
          
          // Wait for page to load
          await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
          return {
            success: false,
            message: `Navigation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            includeInMemory: true,
          };
        }
      }

      // Perform search if required
      if (params.search_query) {
        // This would need to be implemented based on the specific site's search functionality
        stepMessages.push(`Search functionality would be implemented for: "${params.search_query}"`);
      }

      // Execute extraction steps
      for (let i = 0; i < params.extraction_steps.length; i++) {
        const step = params.extraction_steps[i];
        
        try {
          const extractionParams: DataExtractionActionParams = {
            query: step.extraction_query,
            target_fields: step.expected_fields,
            max_items: 50,
            format_preference: 'table',
          };

          const stepResult = await this.extractionAction.execute(extractionParams);
          
          if (stepResult.success && stepResult.data) {
            allResults.push({
              step: i + 1,
              description: step.step_description,
              data: stepResult.data,
              metadata: stepResult.metadata,
            });
            stepMessages.push(`Step ${i + 1}: ${stepResult.message}`);
          } else {
            stepMessages.push(`Step ${i + 1} failed: ${stepResult.message}`);
          }

          // Small delay between steps
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          stepMessages.push(`Step ${i + 1} error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Combine all extracted data
      const combinedData = allResults.flatMap(result => result.data);
      
      // Export final results if requested
      let finalExportResult;
      if (params.final_export_format && combinedData.length > 0) {
        const exportOptions: ExportOptions = {
          format: params.final_export_format,
          filename: `smart_extraction_${Date.now()}.${params.final_export_format}`,
          includeMetadata: true,
        };

        // Create a combined extraction result for export
        const combinedExtractionResult = {
          success: true,
          data: combinedData,
          metadata: {
            source_url: await this.getCurrentUrl(),
            extraction_timestamp: Date.now(),
            total_items: combinedData.length,
            extraction_method: 'smart_multi_step',
            confidence_score: this.calculateAverageConfidence(allResults),
          },
        };

        finalExportResult = await new DataExportService().exportData(combinedExtractionResult, exportOptions);
      }

      // Create final message
      const totalItems = combinedData.length;
      let message = `Smart data extraction completed. `;
      message += `Total items extracted: ${totalItems} across ${allResults.length} steps.\n\n`;
      message += stepMessages.join('\n');
      
      if (finalExportResult?.success) {
        message += `\n\nFinal export: ${finalExportResult.filename} (${this.formatFileSize(finalExportResult.size)})`;
      }

      return {
        success: true,
        results: allResults,
        final_export: finalExportResult,
        message,
        includeInMemory: true,
      };

    } catch (error) {
      logger.error('Smart data extraction failed', error);
      return {
        success: false,
        message: `Smart data extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        includeInMemory: true,
      };
    }
  }

  private async getCurrentUrl(): Promise<string> {
    try {
      const currentPage = await this.context.browserContext.getCurrentPage();
      const pageState = await currentPage.getState(false, false);
      return pageState.url;
    } catch {
      return 'unknown';
    }
  }

  private calculateAverageConfidence(results: any[]): number {
    if (results.length === 0) return 0;
    
    const totalConfidence = results.reduce((sum, result) => {
      return sum + (result.metadata?.confidence_score || 0);
    }, 0);
    
    return totalConfidence / results.length;
  }

  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
