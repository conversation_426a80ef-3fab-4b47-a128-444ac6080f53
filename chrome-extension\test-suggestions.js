/**
 * Context-Aware Suggestions Test Script
 * Run this in the browser console to test basic functionality
 */

// Test configuration
const TEST_CONFIG = {
  testSites: [
    { url: 'https://amazon.com', type: 'ecommerce', expectedSuggestions: ['Compare Prices', 'Find Coupons'] },
    { url: 'https://forms.gle', type: 'form', expectedSuggestions: ['Auto-fill Form', 'Save as Template'] },
    { url: 'https://wikipedia.org', type: 'research', expectedSuggestions: ['Extract Table Data', 'Summarize Content'] },
    { url: 'https://twitter.com', type: 'social_media', expectedSuggestions: ['Schedule Posts', 'Extract Contacts'] }
  ],
  timeout: 10000 // 10 seconds
};

// Test utilities
const TestUtils = {
  log: (message, type = 'info') => {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${type.toUpperCase()}]`;
    console.log(`${prefix} ${message}`);
  },

  error: (message) => TestUtils.log(message, 'error'),
  success: (message) => TestUtils.log(message, 'success'),
  warn: (message) => TestUtils.log(message, 'warn'),

  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  checkExtensionInstalled: () => {
    return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
  },

  connectToExtension: () => {
    if (!TestUtils.checkExtensionInstalled()) {
      throw new Error('Extension not installed or not accessible');
    }
    return chrome.runtime.connect({ name: 'side-panel-connection' });
  }
};

// Test cases
const Tests = {
  async testExtensionConnection() {
    TestUtils.log('Testing extension connection...');
    
    try {
      const port = TestUtils.connectToExtension();
      TestUtils.success('Extension connection established');
      port.disconnect();
      return true;
    } catch (error) {
      TestUtils.error(`Extension connection failed: ${error.message}`);
      return false;
    }
  },

  async testSuggestionGeneration() {
    TestUtils.log('Testing suggestion generation...');
    
    try {
      const port = TestUtils.connectToExtension();
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          TestUtils.error('Suggestion generation timed out');
          port.disconnect();
          resolve(false);
        }, TEST_CONFIG.timeout);

        port.onMessage.addListener((message) => {
          if (message.type === 'suggestions_response') {
            clearTimeout(timeout);
            
            if (message.suggestions && message.suggestions.length > 0) {
              TestUtils.success(`Generated ${message.suggestions.length} suggestions`);
              TestUtils.log(`Context: ${message.analysis?.contextType || 'unknown'}`);
              TestUtils.log(`Confidence: ${Math.round((message.analysis?.confidence || 0) * 100)}%`);
              
              message.suggestions.forEach((suggestion, index) => {
                TestUtils.log(`  ${index + 1}. ${suggestion.title} (${suggestion.category})`);
              });
              
              port.disconnect();
              resolve(true);
            } else {
              TestUtils.warn('No suggestions generated');
              port.disconnect();
              resolve(false);
            }
          } else if (message.type === 'error') {
            clearTimeout(timeout);
            TestUtils.error(`Suggestion generation error: ${message.error}`);
            port.disconnect();
            resolve(false);
          }
        });

        port.postMessage({ type: 'get_suggestions', forceRefresh: true });
      });
    } catch (error) {
      TestUtils.error(`Suggestion generation test failed: ${error.message}`);
      return false;
    }
  },

  async testAnalytics() {
    TestUtils.log('Testing analytics functionality...');
    
    try {
      const port = TestUtils.connectToExtension();
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          TestUtils.error('Analytics test timed out');
          port.disconnect();
          resolve(false);
        }, TEST_CONFIG.timeout);

        port.onMessage.addListener((message) => {
          if (message.type === 'analytics_response') {
            clearTimeout(timeout);
            
            TestUtils.success('Analytics data retrieved');
            TestUtils.log(`Analytics entries: ${Object.keys(message.analytics || {}).length}`);
            TestUtils.log(`Most used categories: ${message.insights?.mostUsedCategories?.join(', ') || 'none'}`);
            
            port.disconnect();
            resolve(true);
          } else if (message.type === 'error') {
            clearTimeout(timeout);
            TestUtils.error(`Analytics test error: ${message.error}`);
            port.disconnect();
            resolve(false);
          }
        });

        port.postMessage({ type: 'get_suggestion_analytics' });
      });
    } catch (error) {
      TestUtils.error(`Analytics test failed: ${error.message}`);
      return false;
    }
  },

  async testUsageTracking() {
    TestUtils.log('Testing usage tracking...');
    
    try {
      const port = TestUtils.connectToExtension();
      const testSuggestionId = 'test-suggestion-' + Date.now();
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          TestUtils.error('Usage tracking test timed out');
          port.disconnect();
          resolve(false);
        }, TEST_CONFIG.timeout);

        port.onMessage.addListener((message) => {
          if (message.type === 'suggestion_tracked') {
            clearTimeout(timeout);
            TestUtils.success('Usage tracking successful');
            port.disconnect();
            resolve(true);
          }
        });

        port.postMessage({
          type: 'track_suggestion_usage',
          suggestionId: testSuggestionId,
          action: 'clicked'
        });
      });
    } catch (error) {
      TestUtils.error(`Usage tracking test failed: ${error.message}`);
      return false;
    }
  },

  async testUIElements() {
    TestUtils.log('Testing UI elements...');
    
    try {
      // Check if suggestions button exists
      const suggestionsButton = document.querySelector('[title="Smart Suggestions"]');
      if (suggestionsButton) {
        TestUtils.success('Suggestions button found in UI');
        return true;
      } else {
        TestUtils.warn('Suggestions button not found - may need to open side panel first');
        return false;
      }
    } catch (error) {
      TestUtils.error(`UI test failed: ${error.message}`);
      return false;
    }
  }
};

// Main test runner
async function runContextSuggestionsTests() {
  TestUtils.log('Starting Context-Aware Suggestions Tests');
  TestUtils.log('='.repeat(50));
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0
  };

  const testCases = [
    { name: 'Extension Connection', test: Tests.testExtensionConnection },
    { name: 'Suggestion Generation', test: Tests.testSuggestionGeneration },
    { name: 'Analytics', test: Tests.testAnalytics },
    { name: 'Usage Tracking', test: Tests.testUsageTracking },
    { name: 'UI Elements', test: Tests.testUIElements }
  ];

  for (const testCase of testCases) {
    TestUtils.log(`\nRunning: ${testCase.name}`);
    results.total++;
    
    try {
      const passed = await testCase.test();
      if (passed) {
        results.passed++;
        TestUtils.success(`✅ ${testCase.name} PASSED`);
      } else {
        results.failed++;
        TestUtils.error(`❌ ${testCase.name} FAILED`);
      }
    } catch (error) {
      results.failed++;
      TestUtils.error(`❌ ${testCase.name} ERROR: ${error.message}`);
    }
    
    // Wait between tests
    await TestUtils.wait(1000);
  }

  // Summary
  TestUtils.log('\n' + '='.repeat(50));
  TestUtils.log('TEST SUMMARY');
  TestUtils.log('='.repeat(50));
  TestUtils.log(`Total Tests: ${results.total}`);
  TestUtils.log(`Passed: ${results.passed}`);
  TestUtils.log(`Failed: ${results.failed}`);
  TestUtils.log(`Success Rate: ${Math.round((results.passed / results.total) * 100)}%`);
  
  if (results.failed === 0) {
    TestUtils.success('🎉 All tests passed!');
  } else {
    TestUtils.warn(`⚠️  ${results.failed} test(s) failed`);
  }

  return results;
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  TestUtils.log('Context-Aware Suggestions Test Script Loaded');
  TestUtils.log('Run runContextSuggestionsTests() to start testing');
  
  // Make functions available globally
  window.runContextSuggestionsTests = runContextSuggestionsTests;
  window.TestUtils = TestUtils;
  window.Tests = Tests;
}
