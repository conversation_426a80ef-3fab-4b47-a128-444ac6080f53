import { createLogger } from '../log';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import type BrowserContext from '../browser/context';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { z } from 'zod';

const logger = createLogger('DataExtractionService');

// Schema for extracted data structure
export const ExtractedDataSchema = z.object({
  success: z.boolean(),
  data: z.array(z.record(z.string(), z.any())),
  metadata: z.object({
    source_url: z.string(),
    extraction_timestamp: z.number(),
    total_items: z.number(),
    extraction_method: z.string(),
    confidence_score: z.number().min(0).max(1),
  }),
  error: z.string().optional(),
});

export type ExtractedData = z.infer<typeof ExtractedDataSchema>;

// Schema for data extraction request
export const DataExtractionRequestSchema = z.object({
  query: z.string(),
  target_fields: z.array(z.string()).optional(),
  max_items: z.number().default(50),
  format_preference: z.enum(['table', 'list', 'structured']).default('table'),
  include_metadata: z.boolean().default(true),
});

export type DataExtractionRequest = z.infer<typeof DataExtractionRequestSchema>;

// Schema for extraction strategy
export const ExtractionStrategySchema = z.object({
  strategy: z.enum(['dom_analysis', 'content_parsing', 'pattern_matching', 'ai_guided']),
  selectors: z.array(z.string()).optional(),
  patterns: z.array(z.string()).optional(),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
});

export type ExtractionStrategy = z.infer<typeof ExtractionStrategySchema>;

export class DataExtractionService {
  private llm: BaseChatModel;
  private browserContext: BrowserContext;

  constructor(llm: BaseChatModel, browserContext: BrowserContext) {
    this.llm = llm;
    this.browserContext = browserContext;
  }

  /**
   * Main method to extract data from the current page based on natural language query
   */
  async extractData(request: DataExtractionRequest): Promise<ExtractedData> {
    try {
      logger.info('Starting data extraction', { query: request.query });

      // Get current page state
      const currentPage = await this.browserContext.getCurrentPage();
      const pageState = await currentPage.getState(false, false);

      // Analyze the page and determine extraction strategy
      const strategy = await this.analyzeExtractionStrategy(request, pageState);
      logger.info('Extraction strategy determined', strategy);

      // Execute extraction based on strategy
      let extractedData: ExtractedData;
      
      switch (strategy.strategy) {
        case 'ai_guided':
          extractedData = await this.aiGuidedExtraction(request, pageState, strategy);
          break;
        case 'dom_analysis':
          extractedData = await this.domAnalysisExtraction(request, pageState, strategy);
          break;
        case 'content_parsing':
          extractedData = await this.contentParsingExtraction(request, pageState, strategy);
          break;
        case 'pattern_matching':
          extractedData = await this.patternMatchingExtraction(request, pageState, strategy);
          break;
        default:
          throw new Error(`Unsupported extraction strategy: ${strategy.strategy}`);
      }

      // Validate and clean extracted data
      const validatedData = await this.validateAndCleanData(extractedData, request);
      
      logger.info('Data extraction completed', { 
        itemCount: validatedData.data.length,
        confidence: validatedData.metadata.confidence_score 
      });

      return validatedData;

    } catch (error) {
      logger.error('Data extraction failed', error);
      return {
        success: false,
        data: [],
        metadata: {
          source_url: '',
          extraction_timestamp: Date.now(),
          total_items: 0,
          extraction_method: 'failed',
          confidence_score: 0,
        },
        error: error instanceof Error ? error.message : 'Unknown extraction error',
      };
    }
  }

  /**
   * Analyze the page and determine the best extraction strategy
   */
  private async analyzeExtractionStrategy(
    request: DataExtractionRequest,
    pageState: any
  ): Promise<ExtractionStrategy> {
    const systemPrompt = `You are an expert web data extraction analyst. Your task is to analyze a webpage and determine the best strategy for extracting data based on a user's natural language query.

Available extraction strategies:
1. ai_guided: Use AI to intelligently identify and extract data based on semantic understanding
2. dom_analysis: Analyze DOM structure to find patterns and extract data using CSS selectors
3. content_parsing: Parse text content using natural language processing
4. pattern_matching: Use regex and text patterns to extract structured data

Analyze the page structure and content, then recommend the best strategy with confidence score and reasoning.`;

    const userPrompt = `User Query: "${request.query}"
Target Fields: ${request.target_fields?.join(', ') || 'Auto-detect'}
Max Items: ${request.max_items}

Page Information:
- URL: ${pageState.url}
- Title: ${pageState.title}
- DOM Elements: ${this.summarizeDOMStructure(pageState.elementTree)}

Please analyze this page and recommend the best extraction strategy. Consider:
1. The complexity of the data structure
2. The presence of structured elements (tables, lists, cards)
3. The semantic clarity of the content
4. The reliability of potential selectors

Respond with a JSON object matching the ExtractionStrategy schema.`;

    try {
      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt),
      ];

      const response = await this.llm.invoke(messages);
      const strategyText = typeof response.content === 'string' ? response.content : '';
      
      // Extract JSON from response
      const jsonMatch = strategyText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in strategy response');
      }

      const strategyData = JSON.parse(jsonMatch[0]);
      return ExtractionStrategySchema.parse(strategyData);

    } catch (error) {
      logger.warn('Failed to analyze extraction strategy, using fallback', error);
      return {
        strategy: 'ai_guided',
        confidence: 0.5,
        reasoning: 'Fallback strategy due to analysis failure',
      };
    }
  }

  /**
   * AI-guided extraction using LLM to understand and extract data
   */
  private async aiGuidedExtraction(
    request: DataExtractionRequest,
    pageState: any,
    strategy: ExtractionStrategy
  ): Promise<ExtractedData> {
    const systemPrompt = `You are an expert data extraction AI. Your task is to extract structured data from webpage content based on user queries.

Rules:
1. Extract data that matches the user's query intent
2. Structure data as an array of objects with consistent field names
3. Include only relevant and accurate information
4. Provide confidence scores for extraction quality
5. Handle missing or incomplete data gracefully

Output format: JSON object matching the ExtractedData schema.`;

    const userPrompt = `Extract data from this webpage based on the query: "${request.query}"

Page Content:
- URL: ${pageState.url}
- Title: ${pageState.title}
- Visible Text: ${this.extractVisibleText(pageState.elementTree).substring(0, 8000)}

Target Fields: ${request.target_fields?.join(', ') || 'Auto-detect based on query'}
Max Items: ${request.max_items}
Format: ${request.format_preference}

Please extract the requested data and return it as a structured JSON object.`;

    try {
      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt),
      ];

      const response = await this.llm.invoke(messages);
      const responseText = typeof response.content === 'string' ? response.content : '';
      
      // Extract JSON from response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in extraction response');
      }

      const extractedData = JSON.parse(jsonMatch[0]);
      
      // Ensure the response matches our schema
      return ExtractedDataSchema.parse({
        success: true,
        data: extractedData.data || [],
        metadata: {
          source_url: pageState.url,
          extraction_timestamp: Date.now(),
          total_items: extractedData.data?.length || 0,
          extraction_method: 'ai_guided',
          confidence_score: strategy.confidence,
          ...extractedData.metadata,
        },
      });

    } catch (error) {
      logger.error('AI-guided extraction failed', error);
      throw error;
    }
  }

  /**
   * DOM analysis extraction using CSS selectors and structural patterns
   */
  private async domAnalysisExtraction(
    request: DataExtractionRequest,
    pageState: any,
    strategy: ExtractionStrategy
  ): Promise<ExtractedData> {
    try {
      const currentPage = await this.browserContext.getCurrentPage();

      // Use the suggested selectors from strategy or find common patterns
      const selectors = strategy.selectors || this.findCommonSelectors(pageState.elementTree);

      const extractedItems: Record<string, any>[] = [];

      for (const selector of selectors) {
        try {
          // Execute script to extract data using the selector
          const results = await currentPage.evaluateScript(`
            const elements = document.querySelectorAll('${selector}');
            const data = [];
            elements.forEach((el, index) => {
              if (index >= ${request.max_items}) return;

              const item = {};
              // Extract text content
              item.text = el.textContent?.trim() || '';

              // Extract common attributes
              if (el.href) item.href = el.href;
              if (el.src) item.src = el.src;
              if (el.alt) item.alt = el.alt;
              if (el.title) item.title = el.title;

              // Extract data attributes
              for (const attr of el.attributes) {
                if (attr.name.startsWith('data-')) {
                  item[attr.name] = attr.value;
                }
              }

              data.push(item);
            });
            return data;
          `);

          if (results && Array.isArray(results)) {
            extractedItems.push(...results);
          }
        } catch (error) {
          logger.warn(`Failed to extract with selector ${selector}`, error);
        }
      }

      return {
        success: true,
        data: extractedItems.slice(0, request.max_items),
        metadata: {
          source_url: pageState.url,
          extraction_timestamp: Date.now(),
          total_items: extractedItems.length,
          extraction_method: 'dom_analysis',
          confidence_score: strategy.confidence,
        },
      };

    } catch (error) {
      logger.error('DOM analysis extraction failed', error);
      throw error;
    }
  }

  /**
   * Content parsing extraction using text analysis
   */
  private async contentParsingExtraction(
    request: DataExtractionRequest,
    pageState: any,
    strategy: ExtractionStrategy
  ): Promise<ExtractedData> {
    try {
      const visibleText = this.extractVisibleText(pageState.elementTree);

      // Use AI to parse the content and extract structured data
      const systemPrompt = `You are a text parsing expert. Extract structured data from the provided text content based on the user's query.

Rules:
1. Parse the text to identify relevant data points
2. Structure the data as an array of objects
3. Use consistent field names across all items
4. Handle variations in text formatting
5. Extract only data that matches the query intent`;

      const userPrompt = `Query: "${request.query}"
Target Fields: ${request.target_fields?.join(', ') || 'Auto-detect'}
Max Items: ${request.max_items}

Text Content:
${visibleText.substring(0, 10000)}

Please extract the requested data and return it as a JSON object with 'data' array containing the extracted items.`;

      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt),
      ];

      const response = await this.llm.invoke(messages);
      const responseText = typeof response.content === 'string' ? response.content : '';

      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No valid JSON found in content parsing response');
      }

      const parsedData = JSON.parse(jsonMatch[0]);

      return {
        success: true,
        data: parsedData.data || [],
        metadata: {
          source_url: pageState.url,
          extraction_timestamp: Date.now(),
          total_items: parsedData.data?.length || 0,
          extraction_method: 'content_parsing',
          confidence_score: strategy.confidence,
        },
      };

    } catch (error) {
      logger.error('Content parsing extraction failed', error);
      throw error;
    }
  }

  /**
   * Pattern matching extraction using regex and text patterns
   */
  private async patternMatchingExtraction(
    request: DataExtractionRequest,
    pageState: any,
    strategy: ExtractionStrategy
  ): Promise<ExtractedData> {
    try {
      const visibleText = this.extractVisibleText(pageState.elementTree);
      const patterns = strategy.patterns || this.generateCommonPatterns(request.query);

      const extractedItems: Record<string, any>[] = [];

      for (const pattern of patterns) {
        try {
          const regex = new RegExp(pattern, 'gi');
          const matches = visibleText.match(regex);

          if (matches) {
            matches.slice(0, request.max_items).forEach((match, index) => {
              extractedItems.push({
                match: match.trim(),
                pattern: pattern,
                index: index,
              });
            });
          }
        } catch (error) {
          logger.warn(`Invalid regex pattern: ${pattern}`, error);
        }
      }

      return {
        success: true,
        data: extractedItems.slice(0, request.max_items),
        metadata: {
          source_url: pageState.url,
          extraction_timestamp: Date.now(),
          total_items: extractedItems.length,
          extraction_method: 'pattern_matching',
          confidence_score: strategy.confidence,
        },
      };

    } catch (error) {
      logger.error('Pattern matching extraction failed', error);
      throw error;
    }
  }

  /**
   * Validate and clean extracted data
   */
  private async validateAndCleanData(
    data: ExtractedData,
    request: DataExtractionRequest
  ): Promise<ExtractedData> {
    try {
      // Remove duplicates based on content similarity
      const uniqueData = this.removeDuplicates(data.data);

      // Clean and normalize field names
      const cleanedData = uniqueData.map(item => {
        const cleanedItem: Record<string, any> = {};

        for (const [key, value] of Object.entries(item)) {
          // Normalize field names
          const cleanKey = key.toLowerCase().replace(/[^a-z0-9]/g, '_');

          // Clean string values
          if (typeof value === 'string') {
            cleanedItem[cleanKey] = value.trim().replace(/\s+/g, ' ');
          } else {
            cleanedItem[cleanKey] = value;
          }
        }

        return cleanedItem;
      });

      // Filter out empty or invalid items
      const validData = cleanedData.filter(item => {
        const hasContent = Object.values(item).some(value =>
          value !== null && value !== undefined && value !== ''
        );
        return hasContent;
      });

      return {
        ...data,
        data: validData.slice(0, request.max_items),
        metadata: {
          ...data.metadata,
          total_items: validData.length,
        },
      };

    } catch (error) {
      logger.warn('Data validation failed, returning original data', error);
      return data;
    }
  }

  /**
   * Remove duplicate items from extracted data
   */
  private removeDuplicates(data: Record<string, any>[]): Record<string, any>[] {
    const seen = new Set<string>();
    return data.filter(item => {
      // Create a hash of the item content
      const content = JSON.stringify(Object.values(item).sort());
      if (seen.has(content)) {
        return false;
      }
      seen.add(content);
      return true;
    });
  }

  /**
   * Summarize DOM structure for analysis
   */
  private summarizeDOMStructure(elementTree: any): string {
    try {
      const summary: string[] = [];

      const analyzeElement = (element: any, depth: number = 0): void => {
        if (depth > 3) return; // Limit depth to avoid too much detail

        if (element.tagName) {
          const tag = element.tagName.toLowerCase();
          const classes = element.attributes?.class ? `.${element.attributes.class.split(' ').join('.')}` : '';
          const id = element.attributes?.id ? `#${element.attributes.id}` : '';

          summary.push(`${'  '.repeat(depth)}${tag}${id}${classes}`);

          // Analyze children
          if (element.children && Array.isArray(element.children)) {
            element.children.slice(0, 5).forEach((child: any) => {
              analyzeElement(child, depth + 1);
            });
          }
        }
      };

      analyzeElement(elementTree);
      return summary.slice(0, 50).join('\n'); // Limit to 50 lines

    } catch (error) {
      logger.warn('Failed to summarize DOM structure', error);
      return 'Unable to analyze DOM structure';
    }
  }

  /**
   * Extract visible text content from DOM tree
   */
  private extractVisibleText(elementTree: any): string {
    try {
      const textParts: string[] = [];

      const extractText = (element: any): void => {
        if (element.type === 'text' && element.text) {
          const text = element.text.trim();
          if (text.length > 0) {
            textParts.push(text);
          }
        }

        if (element.children && Array.isArray(element.children)) {
          element.children.forEach((child: any) => {
            extractText(child);
          });
        }
      };

      extractText(elementTree);
      return textParts.join(' ').replace(/\s+/g, ' ').trim();

    } catch (error) {
      logger.warn('Failed to extract visible text', error);
      return '';
    }
  }

  /**
   * Find common CSS selectors for data extraction
   */
  private findCommonSelectors(elementTree: any): string[] {
    const selectors = [
      // Common data container selectors
      '[data-testid]',
      '.item, .product, .result, .card',
      'article, .article',
      '.list-item, .grid-item',
      'tr, .row',
      '.content, .description',
      // E-commerce specific
      '.product-item, .product-card',
      '.price, .cost, .amount',
      '.title, .name, .heading',
      '.rating, .stars, .score',
      // General content
      'h1, h2, h3, h4, h5, h6',
      'p, .text, .description',
      'a[href], .link',
      'img[alt], .image',
    ];

    return selectors;
  }

  /**
   * Generate common regex patterns for text extraction
   */
  private generateCommonPatterns(query: string): string[] {
    const patterns = [
      // Price patterns
      '\\$[0-9,]+\\.?[0-9]*',
      '\\$[0-9]+',
      '[0-9,]+\\.[0-9]{2}',
      // Rating patterns
      '[0-9]\\.[0-9]\\s*(?:stars?|out of|/)',
      '[0-9]/[0-9]',
      // Email patterns
      '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}',
      // Phone patterns
      '\\(?[0-9]{3}\\)?[-. ]?[0-9]{3}[-. ]?[0-9]{4}',
      // URL patterns
      'https?://[^\\s]+',
    ];

    // Add query-specific patterns
    if (query.toLowerCase().includes('price')) {
      patterns.unshift('\\$[0-9,]+\\.?[0-9]*', 'price:?\\s*\\$?[0-9,]+\\.?[0-9]*');
    }

    if (query.toLowerCase().includes('rating')) {
      patterns.unshift('[0-9]\\.[0-9]\\s*(?:stars?|out of)', '[0-9]/5');
    }

    return patterns;
  }
}
