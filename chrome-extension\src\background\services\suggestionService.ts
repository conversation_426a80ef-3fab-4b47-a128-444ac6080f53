import { createLogger } from '../log';
import { ContextAnalyzer, type ContextAnalysis, type TaskSuggestion } from './contextAnalyzer';
import type BrowserContext from '../browser/context';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { generalSettingsStore } from '@extension/storage';
import templateStorage, { type TaskTemplate } from '@extension/storage/lib/templates/storage';

const logger = createLogger('SuggestionService');

export interface SuggestionServiceConfig {
  enabled: boolean;
  maxSuggestions: number;
  minConfidence: number;
  cacheDuration: number; // in milliseconds
}

const DEFAULT_CONFIG: SuggestionServiceConfig = {
  enabled: true,
  maxSuggestions: 5,
  minConfidence: 0.3,
  cacheDuration: 5 * 60 * 1000 // 5 minutes
};

export class SuggestionService {
  private contextAnalyzer: ContextAnalyzer;
  private browserContext: BrowserContext;
  private config: SuggestionServiceConfig;
  private lastAnalysis: ContextAnalysis | null = null;
  private lastAnalysisTime: number = 0;

  constructor(llm: BaseChatModel, browserContext: BrowserContext, config?: Partial<SuggestionServiceConfig>) {
    this.contextAnalyzer = new ContextAnalyzer(llm);
    this.browserContext = browserContext;
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Get contextual suggestions for the current page
   */
  async getSuggestions(forceRefresh = false): Promise<TaskSuggestion[]> {
    try {
      if (!this.config.enabled) {
        logger.debug('Suggestion service is disabled');
        return [];
      }

      // Check if we have recent analysis and don't need to refresh
      const now = Date.now();
      if (!forceRefresh && 
          this.lastAnalysis && 
          (now - this.lastAnalysisTime) < this.config.cacheDuration) {
        logger.debug('Returning cached suggestions');
        return this.filterSuggestions(this.lastAnalysis.suggestions);
      }

      // Get current page state
      const pageState = await this.browserContext.getCachedState();
      if (!pageState || !pageState.url || pageState.url === 'about:blank') {
        logger.debug('No valid page state for suggestions');
        return [];
      }

      // Analyze context
      logger.info('Generating new suggestions for page', { url: pageState.url });
      const analysis = await this.contextAnalyzer.analyzeContext(pageState);
      
      // Cache the analysis
      this.lastAnalysis = analysis;
      this.lastAnalysisTime = now;

      // Enhance suggestions with relevant templates
      const enhancedSuggestions = await this.enhanceSuggestionsWithTemplates(analysis.suggestions);

      // Improve suggestions based on analytics
      const improvedSuggestions = await this.improveSuggestionsWithAnalytics(enhancedSuggestions);

      // Filter and return suggestions
      const filteredSuggestions = this.filterSuggestions(improvedSuggestions);

      logger.info('Generated suggestions', {
        count: filteredSuggestions.length,
        contextType: analysis.contextType,
        confidence: analysis.confidence,
        enhancedWithTemplates: enhancedSuggestions.length > analysis.suggestions.length
      });

      return filteredSuggestions;
    } catch (error) {
      logger.error('Failed to get suggestions', error);
      return [];
    }
  }

  /**
   * Get the current context analysis
   */
  getCurrentAnalysis(): ContextAnalysis | null {
    return this.lastAnalysis;
  }

  /**
   * Filter suggestions based on configuration
   */
  private filterSuggestions(suggestions: TaskSuggestion[]): TaskSuggestion[] {
    return suggestions
      .filter(suggestion => suggestion.confidence >= this.config.minConfidence)
      .slice(0, this.config.maxSuggestions);
  }

  /**
   * Update service configuration
   */
  updateConfig(newConfig: Partial<SuggestionServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Suggestion service config updated', this.config);
  }

  /**
   * Clear cached analysis
   */
  clearCache(): void {
    this.lastAnalysis = null;
    this.lastAnalysisTime = 0;
    this.contextAnalyzer.clearCache();
    logger.debug('Suggestion cache cleared');
  }

  /**
   * Check if suggestions are available for current page
   */
  async hasSuggestions(): Promise<boolean> {
    const suggestions = await this.getSuggestions();
    return suggestions.length > 0;
  }

  /**
   * Get suggestion by ID
   */
  getSuggestionById(id: string): TaskSuggestion | null {
    if (!this.lastAnalysis) {
      return null;
    }
    
    return this.lastAnalysis.suggestions.find(s => s.id === id) || null;
  }

  /**
   * Track suggestion usage for analytics
   */
  async trackSuggestionUsage(suggestionId: string, action: 'viewed' | 'clicked' | 'executed'): Promise<void> {
    try {
      // Get current analytics data
      const settings = await generalSettingsStore.getSettings();
      const analytics = settings.suggestionAnalytics || {};
      
      // Initialize suggestion analytics if not exists
      if (!analytics[suggestionId]) {
        analytics[suggestionId] = {
          viewed: 0,
          clicked: 0,
          executed: 0,
          firstSeen: Date.now(),
          lastUsed: Date.now()
        };
      }

      // Update counters
      analytics[suggestionId][action]++;
      analytics[suggestionId].lastUsed = Date.now();

      // Save back to storage
      await generalSettingsStore.updateSettings({
        suggestionAnalytics: analytics
      });

      logger.debug('Tracked suggestion usage', { suggestionId, action });
    } catch (error) {
      logger.error('Failed to track suggestion usage', error);
    }
  }

  /**
   * Get usage analytics for suggestions
   */
  async getUsageAnalytics(): Promise<Record<string, any>> {
    try {
      const settings = await generalSettingsStore.getSettings();
      return settings.suggestionAnalytics || {};
    } catch (error) {
      logger.error('Failed to get usage analytics', error);
      return {};
    }
  }

  /**
   * Get analytics insights for improving suggestions
   */
  async getAnalyticsInsights(): Promise<{
    mostUsedCategories: string[];
    mostSuccessfulSuggestions: string[];
    averageConfidenceThreshold: number;
    usagePatterns: Record<string, number>;
  }> {
    try {
      const analytics = await this.getUsageAnalytics();
      const insights = {
        mostUsedCategories: [] as string[],
        mostSuccessfulSuggestions: [] as string[],
        averageConfidenceThreshold: 0.3,
        usagePatterns: {} as Record<string, number>
      };

      if (Object.keys(analytics).length === 0) {
        return insights;
      }

      // Analyze category usage
      const categoryUsage: Record<string, number> = {};
      const suggestionSuccess: Array<{ id: string; score: number }> = [];

      for (const [suggestionId, data] of Object.entries(analytics)) {
        const suggestion = await this.getSuggestionById(suggestionId);
        if (suggestion) {
          const category = suggestion.category;
          categoryUsage[category] = (categoryUsage[category] || 0) + data.clicked + data.executed;

          // Calculate success score (executed > clicked > viewed)
          const successScore = (data.executed * 3) + (data.clicked * 2) + (data.viewed * 1);
          suggestionSuccess.push({ id: suggestionId, score: successScore });
        }
      }

      // Sort and get top categories
      insights.mostUsedCategories = Object.entries(categoryUsage)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 3)
        .map(([category]) => category);

      // Sort and get most successful suggestions
      insights.mostSuccessfulSuggestions = suggestionSuccess
        .sort((a, b) => b.score - a.score)
        .slice(0, 5)
        .map(item => item.id);

      // Calculate usage patterns by time of day
      const now = Date.now();
      const dayInMs = 24 * 60 * 60 * 1000;

      for (const data of Object.values(analytics)) {
        const daysSinceLastUsed = Math.floor((now - data.lastUsed) / dayInMs);
        const pattern = daysSinceLastUsed < 1 ? 'recent' :
                       daysSinceLastUsed < 7 ? 'weekly' : 'older';
        insights.usagePatterns[pattern] = (insights.usagePatterns[pattern] || 0) + 1;
      }

      logger.info('Generated analytics insights', insights);
      return insights;
    } catch (error) {
      logger.error('Failed to get analytics insights', error);
      return {
        mostUsedCategories: [],
        mostSuccessfulSuggestions: [],
        averageConfidenceThreshold: 0.3,
        usagePatterns: {}
      };
    }
  }

  /**
   * Improve suggestions based on analytics
   */
  async improveSuggestionsWithAnalytics(suggestions: TaskSuggestion[]): Promise<TaskSuggestion[]> {
    try {
      const insights = await this.getAnalyticsInsights();

      // Boost suggestions from popular categories
      const improvedSuggestions = suggestions.map(suggestion => {
        let boostedSuggestion = { ...suggestion };

        // Boost confidence for popular categories
        if (insights.mostUsedCategories.includes(suggestion.category)) {
          boostedSuggestion.confidence = Math.min(1.0, suggestion.confidence + 0.1);
          boostedSuggestion.priority = Math.min(5, suggestion.priority + 1);
        }

        // Boost successful suggestion patterns
        if (insights.mostSuccessfulSuggestions.some(id => id.includes(suggestion.category))) {
          boostedSuggestion.confidence = Math.min(1.0, suggestion.confidence + 0.05);
        }

        return boostedSuggestion;
      });

      // Sort by improved confidence and priority
      return improvedSuggestions.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return b.confidence - a.confidence;
      });

    } catch (error) {
      logger.error('Failed to improve suggestions with analytics', error);
      return suggestions;
    }
  }

  /**
   * Get relevant templates based on current context
   */
  async getRelevantTemplates(contextType?: string, category?: string): Promise<TaskTemplate[]> {
    try {
      const allTemplates = await templateStorage.getAllTemplates();

      if (!contextType && !category) {
        return allTemplates.slice(0, 3); // Return first 3 templates
      }

      // Filter templates based on context and category
      const relevantTemplates = allTemplates.filter(template => {
        const templateCategory = template.category.toLowerCase();
        const templateContent = template.content.toLowerCase();

        // Match by category
        if (category) {
          const categoryMatch = templateCategory.includes(category.toLowerCase()) ||
                               templateContent.includes(category.toLowerCase());
          if (categoryMatch) return true;
        }

        // Match by context type
        if (contextType) {
          const contextMatch = templateCategory.includes(contextType.toLowerCase()) ||
                              templateContent.includes(contextType.toLowerCase());
          if (contextMatch) return true;
        }

        return false;
      });

      return relevantTemplates.slice(0, 3); // Return top 3 matches
    } catch (error) {
      logger.error('Failed to get relevant templates', error);
      return [];
    }
  }

  /**
   * Create a template from a suggestion
   */
  async createTemplateFromSuggestion(suggestion: TaskSuggestion): Promise<TaskTemplate | null> {
    try {
      if (!suggestion.template) {
        logger.warn('Cannot create template from suggestion without template content');
        return null;
      }

      const template = await templateStorage.addTemplate(
        suggestion.title,
        suggestion.template,
        suggestion.category.replace('_', ' ')
      );

      logger.info('Created template from suggestion', {
        suggestionId: suggestion.id,
        templateId: template.id
      });

      return template;
    } catch (error) {
      logger.error('Failed to create template from suggestion', error);
      return null;
    }
  }

  /**
   * Enhance suggestions with relevant templates
   */
  async enhanceSuggestionsWithTemplates(suggestions: TaskSuggestion[]): Promise<TaskSuggestion[]> {
    try {
      const enhancedSuggestions = [...suggestions];

      // Get current context if available
      const contextType = this.lastAnalysis?.contextType;

      // Add template-based suggestions
      const relevantTemplates = await this.getRelevantTemplates(contextType);

      for (const template of relevantTemplates) {
        // Create a suggestion from the template
        const templateSuggestion: TaskSuggestion = {
          id: `template-${template.id}`,
          title: `Use Template: ${template.title}`,
          description: `Apply the "${template.title}" template to this page`,
          category: template.category.toLowerCase().replace(' ', '_') as any,
          contextType: contextType || 'unknown',
          confidence: 0.6, // Medium confidence for template suggestions
          template: template.content,
          icon: 'Save',
          priority: 2
        };

        // Add if not already present
        const exists = enhancedSuggestions.some(s =>
          s.title.toLowerCase().includes(template.title.toLowerCase())
        );

        if (!exists) {
          enhancedSuggestions.push(templateSuggestion);
        }
      }

      // Sort by priority and confidence
      return enhancedSuggestions.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return b.confidence - a.confidence;
      });

    } catch (error) {
      logger.error('Failed to enhance suggestions with templates', error);
      return suggestions;
    }
  }

  /**
   * Initialize the service with settings from storage
   */
  async initialize(): Promise<void> {
    try {
      const settings = await generalSettingsStore.getSettings();

      // Update config from settings
      this.updateConfig({
        enabled: settings.contextSuggestionsEnabled ?? true,
        maxSuggestions: settings.maxSuggestions ?? 5,
        minConfidence: settings.minSuggestionConfidence ?? 0.3
      });

      logger.info('Suggestion service initialized', this.config);
    } catch (error) {
      logger.error('Failed to initialize suggestion service', error);
    }
  }
}
