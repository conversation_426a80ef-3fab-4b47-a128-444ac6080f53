import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import type { AgentContext } from '../types';
import { Actors, ExecutionState } from '../event/types';
import { createLogger } from '../../log';

const logger = createLogger('ExporterAgent');

export interface ExportRequest {
  data: any[];
  format: 'csv' | 'json' | 'txt';
  filename?: string;
  metadata?: any;
}

export interface ExportResult {
  success: boolean;
  content: string;
  filename: string;
  mimeType: string;
  error?: string;
}

export class ExporterAgent {
  private context: AgentContext;
  private llm: BaseChatModel;

  constructor(context: AgentContext, llm: BaseChatModel) {
    this.context = context;
    this.llm = llm;
  }

  /**
   * Check if the user request contains export intent
   */
  shouldActivate(userMessage: string): boolean {
    const exportKeywords = [
      'export', 'download', 'save as', 'convert to', 'generate file',
      'csv', 'json', 'txt', 'text file', 'spreadsheet',
      'save data', 'export data', 'download data'
    ];

    const message = userMessage.toLowerCase();
    return exportKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * Determine export format from user request
   */
  private determineFormat(userMessage: string): 'csv' | 'json' | 'txt' {
    const message = userMessage.toLowerCase();
    
    if (message.includes('csv') || message.includes('spreadsheet') || message.includes('excel')) {
      return 'csv';
    }
    if (message.includes('json')) {
      return 'json';
    }
    return 'txt'; // Default fallback
  }

  /**
   * Execute export process
   */
  async execute(userMessage: string): Promise<ExportResult> {
    try {
      logger.info('Exporter agent activated', { message: userMessage });
      
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_START, 'Preparing data export...');

      // Get the last extraction result from context
      const extractedData = this.context.lastExtractionResult;
      
      if (!extractedData || !extractedData.data || extractedData.data.length === 0) {
        const errorMsg = 'No data available to export. Please extract data first.';
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
        return {
          success: false,
          content: '',
          filename: 'no_data',
          mimeType: 'text/plain',
          error: errorMsg,
        };
      }

      // Determine export format
      const format = this.determineFormat(userMessage);
      
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, `Converting data to ${format.toUpperCase()} format...`);

      // Use AI to format the data
      const exportResult = await this.formatDataWithAI(extractedData.data, format, extractedData.metadata);

      if (exportResult.success) {
        // Generate filename
        const filename = this.generateFilename(extractedData.metadata, format);
        
        // Create download
        await this.createDownload(exportResult.content, filename, exportResult.mimeType);
        
        const successMsg = `Successfully exported ${extractedData.data.length} items to ${filename}`;
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_OK, successMsg);
        
        return {
          success: true,
          content: exportResult.content,
          filename: filename,
          mimeType: exportResult.mimeType,
        };
      } else {
        this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, exportResult.error || 'Export failed');
        return exportResult;
      }

    } catch (error) {
      const errorMsg = `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error('Export execution failed', error);
      this.context.emitEvent(Actors.EXPORTER, ExecutionState.ACT_FAIL, errorMsg);
      
      return {
        success: false,
        content: '',
        filename: 'export_failed',
        mimeType: 'text/plain',
        error: errorMsg,
      };
    }
  }

  /**
   * Use AI to format data into the requested format
   */
  private async formatDataWithAI(data: any[], format: string, metadata?: any): Promise<ExportResult> {
    try {
      const systemPrompt = `You are a data formatting expert. Your task is to convert structured data into the requested format.

Rules:
1. For CSV: Create proper CSV with headers, escape commas and quotes correctly
2. For JSON: Create well-formatted JSON with proper structure
3. For TXT: Create readable text format with clear structure
4. Always include all data fields
5. Handle missing or null values gracefully
6. Return ONLY the formatted data, no explanations

Format requested: ${format.toUpperCase()}`;

      const userPrompt = `Convert this data to ${format.toUpperCase()} format:

Data (${data.length} items):
${JSON.stringify(data.slice(0, 5), null, 2)}${data.length > 5 ? '\n... and ' + (data.length - 5) + ' more items' : ''}

${metadata ? `Metadata:
- Source: ${metadata.source_url}
- Extracted: ${new Date(metadata.extraction_timestamp).toISOString()}
- Method: ${metadata.extraction_method}
- Confidence: ${Math.round(metadata.confidence_score * 100)}%` : ''}

Please convert ALL ${data.length} items to ${format.toUpperCase()} format.`;

      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt),
      ];

      const response = await this.llm.invoke(messages);
      const content = typeof response.content === 'string' ? response.content : '';

      // Determine MIME type
      let mimeType = 'text/plain';
      if (format === 'csv') mimeType = 'text/csv';
      if (format === 'json') mimeType = 'application/json';

      return {
        success: true,
        content: content.trim(),
        filename: '',
        mimeType: mimeType,
      };

    } catch (error) {
      logger.error('AI formatting failed', error);
      return {
        success: false,
        content: '',
        filename: '',
        mimeType: 'text/plain',
        error: error instanceof Error ? error.message : 'Formatting failed',
      };
    }
  }

  /**
   * Generate appropriate filename
   */
  private generateFilename(metadata: any, format: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    let baseName = 'extracted_data';
    
    if (metadata?.source_url) {
      try {
        const domain = new URL(metadata.source_url).hostname.replace(/[^a-zA-Z0-9]/g, '_');
        baseName = domain;
      } catch {
        // Keep default name if URL parsing fails
      }
    }
    
    return `${baseName}_${timestamp}.${format}`;
  }

  /**
   * Create download using browser APIs
   */
  private async createDownload(content: string, filename: string, mimeType: string): Promise<void> {
    try {
      // Create blob and object URL
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);

      // Use Chrome downloads API
      if (typeof chrome !== 'undefined' && chrome?.downloads) {
        await new Promise<void>((resolve, reject) => {
          chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true,
          }, (downloadId) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve();
            }
          });
        });
      } else {
        throw new Error('Chrome downloads API not available');
      }

      // Clean up object URL after a delay
      setTimeout(() => URL.revokeObjectURL(url), 10000);

    } catch (error) {
      logger.error('Download creation failed', error);
      throw error;
    }
  }
}
